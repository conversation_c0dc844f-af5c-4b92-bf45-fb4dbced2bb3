package com.laery0.po;

import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 管理员实体类
 * <p>
 * 对应数据库中的admin表，存储管理员的基本信息。
 * 该类用于管理员身份验证和权限管理，包含管理员的用户名和密码等信息。
 * 使用MyBatis-Plus注解进行ORM映射。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class Admin {

    /**
     * 管理员ID，主键
     * 管理员的唯一标识符
     */
    @TableId
    private Integer id;

    /**
     * 管理员用户名
     * 用于登录验证的用户名，必须唯一
     */
    private String username;

    /**
     * 管理员密码
     * 用于登录验证的密码，存储时应考虑加密处理
     */
    private String password;

    /**
     * 默认构造函数
     */
    public Admin() {
    }

    /**
     * 完整构造函数
     *
     * @param id       管理员ID
     * @param username 管理员用户名
     * @param password 管理员密码
     */
    public Admin(Integer id, String username, String password) {
        this.id = id;
        this.username = username;
        this.password = password;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "Admin{" +
                "id=" + id +
                ", sname='" + username + '\'' +
                ", passwork='" + password + '\'' +
                '}';
    }
}
