package com.laery0.po;

import com.baomidou.mybatisplus.annotation.TableId;

// 管理员实体类
public class Admin {

    @TableId
    private Integer id; // 管理员ID
    private String username; // 用户名
    private String password; // 密码

    public Admin() {
    }

    public Admin(Integer id, String username, String password) {
        this.id = id;
        this.username = username;
        this.password = password;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "Admin{" +
                "id=" + id +
                ", sname='" + username + '\'' +
                ", passwork='" + password + '\'' +
                '}';
    }
}
