package com.laery0.po;

import com.baomidou.mybatisplus.annotation.TableName;

// 图书详情实体类
@TableName("book_details")
public class BookDetail {

    private Integer bookId; // 关联的图书ID
    private String description; // 图书详细描述
    private String publishDate; // 出版日期

    public BookDetail() {
    }

    public BookDetail(Integer bookId, String description, String publishDate) {
        this.bookId = bookId;
        this.description = description;
        this.publishDate = publishDate;
    }

    public Integer getBookId() {
        return bookId;
    }

    public void setBookId(Integer bookId) {
        this.bookId = bookId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(String publishDate) {
        this.publishDate = publishDate;
    }

    @Override
    public String toString() {
        return "BookDetails{" +
                "bookId=" + bookId +
                ", description='" + description + '\'' +
                ", publishDate='" + publishDate + '\'' +
                '}';
    }
}
