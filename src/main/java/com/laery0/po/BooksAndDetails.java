package com.laery0.po;

/**
 * 图书及详情组合实体类
 * <p>
 * 继承自Books类，用于封装图书基本信息和详情信息的组合数据。
 * 该类主要用于关联查询的结果映射，将books表和book_details表的数据
 * 组合成一个完整的对象，便于前端展示图书的完整信息。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class BooksAndDetails extends Books {

    /**
     * 图书详情信息
     * 包含图书的详细描述、出版日期等扩展信息
     */
    private BookDetails bookDetails;

    /**
     * 默认构造函数
     */
    public BooksAndDetails() {
    }

    /**
     * 构造函数
     *
     * @param bookDetails 图书详情信息
     */
    public BooksAndDetails(BookDetails bookDetails) {
        this.bookDetails = bookDetails;
    }

    /**
     * 获取图书详情信息
     *
     * @return BookDetails 图书详情对象
     */
    public BookDetails getBookDetails() {
        return bookDetails;
    }

    /**
     * 设置图书详情信息
     *
     * @param bookDetails 图书详情对象
     */
    public void setBookDetails(BookDetails bookDetails) {
        this.bookDetails = bookDetails;
    }

    /**
     * 重写toString方法
     *
     * @return 对象的字符串表示
     */
    @Override
    public String toString() {
        return "BooksAndDetails{" +
                "bookDetails=" + bookDetails +
                ", " + super.toString() +
                '}';
    }
}
