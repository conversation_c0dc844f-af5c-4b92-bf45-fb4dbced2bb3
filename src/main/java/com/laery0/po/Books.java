package com.laery0.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 图书实体类
 * <p>
 * 对应数据库中的books表，存储图书的基本信息。
 * 该类是图书管理系统的核心实体，包含图书的标题、作者、价格、库存等基本属性。
 * 使用MyBatis-Plus注解进行ORM映射。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class Books {

    /**
     * 图书ID，主键
     * 使用数据库自增策略生成唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 图书标题
     * 必填字段，存储图书的名称
     */
    private String title;

    /**
     * 图书作者
     * 必填字段，存储图书的作者姓名
     */
    private String author;

    /**
     * 图书价格
     * 可选字段，存储图书的售价，单位为元
     */
    private Double price;

    /**
     * 图书库存数量
     * 可选字段，存储图书的库存数量
     */
    private Integer stock;

    /**
     * 默认构造函数
     */
    public Books() {
    }

    /**
     * 构造函数（不包含ID）
     * 用于创建新图书时使用，ID由数据库自动生成
     *
     * @param title  图书标题
     * @param author 图书作者
     * @param price  图书价格
     * @param stock  图书库存
     */
    public Books(String title, String author, Double price, Integer stock) {
        this.title = title;
        this.author = author;
        this.price = price;
        this.stock = stock;
    }

    /**
     * 完整构造函数
     * 用于从数据库查询结果构造对象时使用
     *
     * @param id     图书ID
     * @param title  图书标题
     * @param author 图书作者
     * @param price  图书价格
     * @param stock  图书库存
     */
    public Books(Integer id, String title, String author, Double price, Integer stock) {
        this.id = id;
        this.title = title;
        this.author = author;
        this.price = price;
        this.stock = stock;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    @Override
    public String toString() {
        return "Book{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", author='" + author + '\'' +
                ", price=" + price +
                ", stock=" + stock +
                '}';
    }
}
