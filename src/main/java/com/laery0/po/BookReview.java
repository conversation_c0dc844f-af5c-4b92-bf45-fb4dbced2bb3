package com.laery0.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

//图书评论类
@TableName("book_reviews")
public class BookReview {
    @TableId(type = IdType.AUTO)
    private Integer id; //评论ID
    private Integer bookId; //关联的图书ID
    private String reviewerName; //评论者姓名
    private String reviewText; //评论内容
    private Integer rating; //评分

    public BookReview() {
    }

    public BookReview(Integer id, Integer bookId, String reviewerName, String reviewText, Integer rating) {
        this.id = id;
        this.bookId = bookId;
        this.reviewerName = reviewerName;
        this.reviewText = reviewText;
        this.rating = rating;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBookId() {
        return bookId;
    }

    public void setBookId(Integer bookId) {
        this.bookId = bookId;
    }

    public String getReviewerName() {
        return reviewerName;
    }

    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }

    public String getReviewText() {
        return reviewText;
    }

    public void setReviewText(String reviewText) {
        this.reviewText = reviewText;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    @Override
    public String toString() {
        return "BookReview{" +
                "id=" + id +
                ", bookId=" + bookId +
                ", reviewerName='" + reviewerName + '\'' +
                ", reviewText='" + reviewText + '\'' +
                ", rating=" + rating +
                '}';
    }
}

