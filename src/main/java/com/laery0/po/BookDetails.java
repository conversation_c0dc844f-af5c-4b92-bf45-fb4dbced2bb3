package com.laery0.po;

/**
 * 图书详情实体类
 * <p>
 * 对应数据库中的book_details表，存储图书的详细信息。
 * 该类是图书主表的扩展，包含图书的详细描述、出版日期等补充信息。
 * 通过book_id字段与图书主表建立关联关系。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class BookDetails {

    /**
     * 关联的图书ID
     * 外键，关联books表的主键
     */
    private Integer book_Id;

    /**
     * 图书详细描述
     * 存储图书的详细介绍、内容简介等信息
     */
    private String description;

    /**
     * 出版日期
     * 存储图书的出版时间，格式通常为字符串
     */
    private String publish_Date;

    /**
     * 默认构造函数
     */
    public BookDetails() {
    }

    /**
     * 完整构造函数
     *
     * @param book_Id      关联的图书ID
     * @param description  图书详细描述
     * @param publish_Date 出版日期
     */
    public BookDetails(Integer book_Id, String description, String publish_Date) {
        this.book_Id = book_Id;
        this.description = description;
        this.publish_Date = publish_Date;
    }

    public Integer getBook_Id() {
        return book_Id;
    }

    public void setBook_Id(Integer book_Id) {
        this.book_Id = book_Id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPublish_Date() {
        return publish_Date;
    }

    public void setPublish_Date(String publish_Date) {
        this.publish_Date = publish_Date;
    }

    @Override
    public String toString() {
        return "BookDetails{" +
                "book_Id=" + book_Id +
                ", description='" + description + '\'' +
                ", publish_Date='" + publish_Date + '\'' +
                '}';
    }
}
