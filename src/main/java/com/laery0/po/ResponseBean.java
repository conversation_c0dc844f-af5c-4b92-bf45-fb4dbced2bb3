package com.laery0.po;

/**
 * 统一响应结果封装类
 * <p>
 * 用于封装所有API接口的返回结果，提供统一的响应格式。
 * 包含状态码、数据和消息三个核心字段，便于前端统一处理响应结果。
 * 使用泛型设计，支持不同类型的数据返回。
 * </p>
 *
 * @param <T> 响应数据的类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class ResponseBean<T> {

    /**
     * 响应数据
     * 泛型类型，可以是任何类型的数据对象
     */
    private T data;

    /**
     * 状态码
     * 用于标识请求处理的结果状态
     * 常用状态码：
     * - 100: 登录成功
     * - 101: 用户名错误
     * - 102: 密码错误
     * - 200: 操作成功
     * - 201: 查询成功但无数据
     * - 300: 操作失败
     * - 400: 参数错误
     * - 500: 系统异常
     */
    private Integer status;

    /**
     * 响应消息
     * 用于描述请求处理的结果信息，便于用户理解
     */
    private String message;

    /**
     * 完整构造函数
     * 用于创建包含数据的成功响应
     *
     * @param data    响应数据
     * @param status  状态码
     * @param message 响应消息
     */
    public ResponseBean(T data, Integer status, String message) {
        this.data = data;
        this.status = status;
        this.message = message;
    }

    /**
     * 简化构造函数
     * 用于创建不包含数据的响应（通常用于错误响应）
     *
     * @param status  状态码
     * @param message 响应消息
     */
    public ResponseBean(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
