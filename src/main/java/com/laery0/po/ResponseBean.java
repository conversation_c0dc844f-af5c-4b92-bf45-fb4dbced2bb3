package com.laery0.po;

// 统一响应结果封装类
public class ResponseBean<T> {

    private T data; // 响应数据
    private Integer status; // 状态码
    private String message; // 响应消息

    public ResponseBean(T data, Integer status, String message) {
        this.data = data;
        this.status = status;
        this.message = message;
    }

    public ResponseBean(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
