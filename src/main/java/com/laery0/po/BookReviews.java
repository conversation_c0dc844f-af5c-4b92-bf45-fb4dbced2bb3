package com.laery0.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

public class BookReviews {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer book_Id;
    private String reviewer_Name;
    private String review_Text;
    private Integer rating;

    public BookReviews() {
    }

    public BookReviews(Integer id, Integer book_Id, String reviewer_Name, String review_Text, Integer rating) {
        this.id = id;
        this.book_Id = book_Id;
        this.reviewer_Name = reviewer_Name;
        this.review_Text = review_Text;
        this.rating = rating;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBook_Id() {
        return book_Id;
    }

    public void setBook_Id(Integer book_Id) {
        this.book_Id = book_Id;
    }

    public String getReviewer_Name() {
        return reviewer_Name;
    }

    public void setReviewer_Name(String reviewer_Name) {
        this.reviewer_Name = reviewer_Name;
    }

    public String getReview_Text() {
        return review_Text;
    }

    public void setReview_Text(String review_Text) {
        this.review_Text = review_Text;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    @Override
    public String toString() {
        return "BookReviews{" +
                "id=" + id +
                ", book_Id=" + book_Id +
                ", reviewer_Name='" + reviewer_Name + '\'' +
                ", review_Text='" + review_Text + '\'' +
                ", rating=" + rating +
                '}';
    }
}

