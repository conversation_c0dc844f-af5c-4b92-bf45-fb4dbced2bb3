package com.laery0.service;

import com.laery0.po.Admin;
import com.laery0.po.ResponseBean;

/**
 * 管理员业务服务接口
 * <p>
 * 定义管理员相关的业务逻辑接口，主要包括管理员身份验证功能。
 * 负责处理管理员登录、权限验证等核心业务逻辑。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AdminService {

    /**
     * 管理员登录验证
     * <p>
     * 验证管理员的用户名和密码，实现管理员身份认证功能。
     * 登录成功后会清空密码字段，确保敏感信息不会返回给客户端。
     * </p>
     *
     * @param admin 包含用户名和密码的管理员对象
     * @return ResponseBean&lt;Admin&gt; 包含登录结果的响应对象
     *         <ul>
     *         <li>登录成功：status=100, data=管理员信息（密码已清空）, message="登录成功"</li>
     *         <li>用户名错误：status=101, data=null, message="用户名错误"</li>
     *         <li>密码错误：status=102, data=null, message="密码错误"</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<Admin> login(Admin admin);
}
