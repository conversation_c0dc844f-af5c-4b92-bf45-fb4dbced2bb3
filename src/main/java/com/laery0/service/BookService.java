package com.laery0.service;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;

import java.util.List;

public interface BookService {
    List<Books> selectAllBook();
    List<BooksAndDetails> selectBookAndDetails();
    List<BooksAndReviews> selectBookAndReviewsByBookId(Integer bookId);
    int addBook(Books books);
    int updateBook(Books books);
    Integer deleteBookAndRDByBookId(Integer bookId);
    List<Books> selectBookByTitle(String title);
    List<Books> selectBookByTitleAndAuthor(String title, String author);
}

