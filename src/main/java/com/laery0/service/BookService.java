package com.laery0.service;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;
import com.laery0.po.ResponseBean;

import java.util.List;

public interface BookService {
    /**
     * 获取所有图书
     */
    ResponseBean<List<Books>> getAllBooks();

    /**
     * 获取所有图书详情
     */
    ResponseBean<List<BooksAndDetails>> getBooksWithDetails();

    /**
     * 根据图书ID获取图书评论
     */
    ResponseBean<List<BooksAndReviews>> getBookReviewsById(Integer bookId);

    /**
     * 创建新图书
     */
    ResponseBean<Integer> createBook(Books books);

    /**
     * 更新图书信息
     */
    ResponseBean<Integer> updateBook(Books books);

    /**
     * 删除图书及相关数据
     */
    ResponseBean<Integer> deleteBookWithRelatedData(Integer bookId);

    /**
     * 根据标题查找图书
     */
    ResponseBean<List<Books>> findBooksByTitle(String title);

    /**
     * 根据标题和作者查找图书
     */
    ResponseBean<List<Books>> findBooksByTitleAndAuthor(String title, String author);
}

