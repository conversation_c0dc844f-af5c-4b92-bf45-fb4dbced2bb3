package com.laery0.service;

import com.laery0.po.Book;
import com.laery0.po.BookAndDetail;
import com.laery0.po.ResponseBean;

import java.util.List;

// 图书业务服务接口
public interface BookService {

    // 获取所有图书
    ResponseBean<List<Book>> getAllBooks();

    // 获取图书详情
    ResponseBean<List<BookAndDetail>> getBooksWithDetails();

    // 创建图书
    ResponseBean<Integer> createBook(Book book);

    // 更新图书
    ResponseBean<Integer> updateBook(Book book);

    // 删除图书及相关数据
    ResponseBean<Integer> deleteBookWithRelatedData(Integer bookId);

    // 分页查询所有图书
    ResponseBean<List<Book>> getBooksPage(Double price, String author, Long currentPage, Long rows);
}
