package com.laery0.service;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;
import com.laery0.po.ResponseBean;

import java.util.List;

/**
 * 图书业务服务接口
 * <p>
 * 定义图书管理系统的核心业务逻辑接口，包括图书的增删改查、搜索等功能。
 * 所有方法都返回统一的ResponseBean格式，包含状态码、数据和消息信息。
 * 该接口遵循业务逻辑与数据访问分离的原则，专注于业务规则的定义。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface BookService {

    /**
     * 获取所有图书信息
     * <p>
     * 查询系统中的所有图书记录，返回图书基本信息列表。
     * 该方法不包含分页功能，适用于数据量较小的场景。
     * </p>
     *
     * @return ResponseBean&lt;List&lt;Books&gt;&gt; 包含图书列表的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=图书列表, message="查询所有图书成功"</li>
     *         <li>无数据时：status=201, data=空列表, message="暂无图书数据"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<List<Books>> getAllBooks();

    /**
     * 获取所有图书详情信息
     * <p>
     * 查询图书及其详细信息，包含图书基本信息和扩展详情（如描述、出版日期等）。
     * 通过关联查询实现，返回的数据更加完整。
     * </p>
     *
     * @return ResponseBean&lt;List&lt;BooksAndDetails&gt;&gt; 包含图书详情列表的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=图书详情列表, message="查询图书详情成功"</li>
     *         <li>无数据时：status=201, data=空列表, message="暂无图书详情数据"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<List<BooksAndDetails>> getBooksWithDetails();

    /**
     * 根据图书ID获取图书评论信息
     * <p>
     * 通过图书ID查询该图书的所有评论信息，包括评分、评论者姓名、评论内容等。
     * 返回的数据包含图书基本信息和关联的评论列表。
     * </p>
     *
     * @param bookId 图书ID，必须为正整数，不能为null
     * @return ResponseBean&lt;List&lt;BooksAndReviews&gt;&gt; 包含图书评论信息的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=图书评论列表, message="查询图书评论成功"</li>
     *         <li>无数据时：status=201, data=空列表, message="该图书暂无评论数据"</li>
     *         <li>参数错误时：status=400, data=null, message="图书ID不能为空或小于等于0"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<List<BooksAndReviews>> getBookReviewsById(Integer bookId);

    /**
     * 创建新图书
     * <p>
     * 向系统中添加一本新图书，会验证必填字段的有效性。
     * 图书标题和作者为必填项，价格和库存为可选项。
     * </p>
     *
     * @param books 图书信息对象，包含标题、作者、价格、库存等信息
     * @return ResponseBean&lt;Integer&gt; 包含操作结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=影响行数, message="添加图书成功"</li>
     *         <li>失败时：status=300, data=0, message="添加图书失败"</li>
     *         <li>参数错误时：status=400, data=null, message=具体错误信息</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<Integer> createBook(Books books);

    /**
     * 更新图书信息
     * <p>
     * 根据图书ID更新图书的基本信息，支持部分字段更新。
     * 只更新非空字段，空字段保持原值不变。
     * </p>
     *
     * @param books 包含更新信息的图书对象，ID字段必须有效
     * @return ResponseBean&lt;Integer&gt; 包含操作结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=影响行数, message="更新图书成功"</li>
     *         <li>失败时：status=300, data=0, message="更新图书失败，可能图书不存在"</li>
     *         <li>参数错误时：status=400, data=null, message=具体错误信息</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<Integer> updateBook(Books books);

    /**
     * 删除图书及相关数据
     * <p>
     * 根据图书ID删除图书记录，同时删除相关的详情信息和评论数据。
     * 该操作使用事务保证数据一致性，要么全部删除成功，要么全部回滚。
     * 删除顺序：评论 -> 详情 -> 图书，避免外键约束问题。
     * </p>
     *
     * @param bookId 图书ID，必须为正整数，不能为null
     * @return ResponseBean&lt;Integer&gt; 包含操作结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=删除总行数, message="删除图书及相关数据成功"</li>
     *         <li>失败时：status=300, data=0, message="删除失败，可能图书不存在"</li>
     *         <li>参数错误时：status=400, data=null, message="图书ID不能为空或小于等于0"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<Integer> deleteBookWithRelatedData(Integer bookId);

    /**
     * 根据标题查找图书
     * <p>
     * 使用模糊匹配的方式根据图书标题搜索图书信息。
     * 采用左模糊匹配策略，即搜索以指定标题结尾的图书。
     * </p>
     *
     * @param title 图书标题关键字，不能为空或空字符串
     * @return ResponseBean&lt;List&lt;Books&gt;&gt; 包含搜索结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=匹配的图书列表, message="根据标题查询图书成功"</li>
     *         <li>无结果时：status=201, data=空列表, message="未找到匹配的图书"</li>
     *         <li>参数错误时：status=400, data=null, message="图书标题不能为空"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<List<Books>> findBooksByTitle(String title);

    /**
     * 根据标题和作者查找图书
     * <p>
     * 支持同时根据图书标题和作者进行模糊搜索，两个参数都是可选的，
     * 但至少需要提供一个参数。支持单独按标题搜索、单独按作者搜索，
     * 或者同时按两个条件搜索（AND关系）。
     * </p>
     *
     * @param title  图书标题关键字，可以为空
     * @param author 作者姓名关键字，可以为空
     * @return ResponseBean&lt;List&lt;Books&gt;&gt; 包含搜索结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=匹配的图书列表, message="根据标题和作者查询图书成功"</li>
     *         <li>无结果时：status=201, data=空列表, message="未找到匹配的图书"</li>
     *         <li>参数错误时：status=400, data=null, message="图书标题和作者不能同时为空"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @since 1.0
     */
    ResponseBean<List<Books>> findBooksByTitleAndAuthor(String title, String author);
}
