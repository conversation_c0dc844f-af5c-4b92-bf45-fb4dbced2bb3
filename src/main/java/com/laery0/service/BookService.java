package com.laery0.service;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;
import com.laery0.po.ResponseBean;

import java.util.List;

public interface BookService {
    ResponseBean<List<Books>> selectAllBook();
    ResponseBean<List<BooksAndDetails>> selectBookAndDetails();
    ResponseBean<List<BooksAndReviews>> selectBookAndReviewsByBookId(Integer bookId);
    ResponseBean<Integer> addBook(Books books);
    ResponseBean<Integer> updateBook(Books books);
    ResponseBean<Integer> deleteBookAndRDByBookId(Integer bookId);
    ResponseBean<List<Books>> selectBookByTitle(String title);
    ResponseBean<List<Books>> selectBookByTitleAndAuthor(String title, String author);
}

