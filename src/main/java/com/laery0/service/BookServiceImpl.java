package com.laery0.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.laery0.dao.BookDao;
import com.laery0.dao.BookDetailsDao;
import com.laery0.dao.BookReviewsDao;
import com.laery0.po.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class BookServiceImpl implements BookService {
    @Autowired
    private BookDao bookDao;
    @Autowired
    private BookReviewsDao bookReviewsDao;
    @Autowired
    private BookDetailsDao bookDetailsDao;

    @Override
    public ResponseBean<List<Books>> selectAllBook() {
        try {
            List<Books> books = bookDao.selectList(null);
            if (books != null && !books.isEmpty()) {
                return new ResponseBean<>(books, 200, "查询所有图书成功");
            } else {
                return new ResponseBean<>(books, 201, "暂无图书数据");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "查询所有图书失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<List<BooksAndDetails>> selectBookAndDetails() {
        try {
            List<BooksAndDetails> booksAndDetails = bookDao.selectBookAndDetails();
            if (booksAndDetails != null && !booksAndDetails.isEmpty()) {
                return new ResponseBean<>(booksAndDetails, 200, "查询图书详情成功");
            } else {
                return new ResponseBean<>(booksAndDetails, 201, "暂无图书详情数据");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "查询图书详情失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<List<BooksAndReviews>> selectBookAndReviewsByBookId(Integer bookId) {
        try {
            if (bookId == null || bookId <= 0) {
                return new ResponseBean<>(400, "图书ID不能为空或小于等于0");
            }
            List<BooksAndReviews> booksAndReviews = bookDao.selectBookAndReviewsByBookId(bookId);
            if (booksAndReviews != null && !booksAndReviews.isEmpty()) {
                return new ResponseBean<>(booksAndReviews, 200, "查询图书评论成功");
            } else {
                return new ResponseBean<>(booksAndReviews, 201, "该图书暂无评论数据");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "查询图书评论失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<Integer> addBook(Books books) {
        try {
            if (books == null) {
                return new ResponseBean<>(400, "图书信息不能为空");
            }
            if (books.getTitle() == null || books.getTitle().trim().isEmpty()) {
                return new ResponseBean<>(400, "图书标题不能为空");
            }
            if (books.getAuthor() == null || books.getAuthor().trim().isEmpty()) {
                return new ResponseBean<>(400, "图书作者不能为空");
            }
            int result = bookDao.insert(books);
            if (result > 0) {
                return new ResponseBean<>(result, 200, "添加图书成功");
            } else {
                return new ResponseBean<>(result, 300, "添加图书失败");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "添加图书失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<Integer> updateBook(Books books) {
        try {
            if (books == null) {
                return new ResponseBean<>(400, "图书信息不能为空");
            }
            if (books.getId() == null || books.getId() <= 0) {
                return new ResponseBean<>(400, "图书ID不能为空或小于等于0");
            }
            int result = bookDao.updateBook(books);
            if (result > 0) {
                return new ResponseBean<>(result, 200, "更新图书成功");
            } else {
                return new ResponseBean<>(result, 300, "更新图书失败，可能图书不存在");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "更新图书失败：" + e.getMessage());
        }
    }

    @Transactional
    @Override
    public ResponseBean<Integer> deleteBookAndRDByBookId(Integer bookId) {
        try {
            if (bookId == null || bookId <= 0) {
                return new ResponseBean<>(400, "图书ID不能为空或小于等于0");
            }
            QueryWrapper<BookReviews> bookReviewsQueryWrapper = new QueryWrapper<>();
            bookReviewsQueryWrapper.eq("book_id", bookId);
            Integer deletedReviews = bookReviewsDao.delete(bookReviewsQueryWrapper);
            QueryWrapper<BookDetails> bookDetailsQueryWrapper = new QueryWrapper<>();
            bookDetailsQueryWrapper.eq("book_id", bookId);
            Integer deletedDetails = bookDetailsDao.delete(bookDetailsQueryWrapper);
//        Integer x = 10 / 0;
            QueryWrapper<Books> booksQueryWrapper = new QueryWrapper<>();
            booksQueryWrapper.eq("book_id", bookId);
            Integer deletedBook = bookDao.delete(booksQueryWrapper);
            deletedReviews = deletedReviews == null ? 0 : deletedReviews;
            deletedDetails = deletedDetails == null ? 0 : deletedDetails;
            deletedBook = deletedBook == null ? 0 : deletedBook;
            Integer totalDeleted = deletedBook + deletedReviews + deletedDetails;
            if (totalDeleted > 0) {
                return new ResponseBean<>(totalDeleted, 200, "删除图书及相关数据成功");
            } else {
                return new ResponseBean<>(totalDeleted, 300, "删除失败，可能图书不存在");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "删除图书失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<List<Books>> selectBookByTitle(String title) {
        try {
            if (title == null || title.trim().isEmpty()) {
                return new ResponseBean<>(400, "图书标题不能为空");
            }
            QueryWrapper<Books> booksQueryWrapper = new QueryWrapper<>();
            booksQueryWrapper.likeLeft("title", title);
            List<Books> books = bookDao.selectList(booksQueryWrapper);
            if (books != null && !books.isEmpty()) {
                return new ResponseBean<>(books, 200, "根据标题查询图书成功");
            } else {
                return new ResponseBean<>(books, 201, "未找到匹配的图书");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "根据标题查询图书失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<List<Books>> selectBookByTitleAndAuthor(String title, String author) {
        try {
            if ((title == null || title.trim().isEmpty()) && (author == null || author.trim().isEmpty())) {
                return new ResponseBean<>(400, "图书标题和作者不能同时为空");
            }
            QueryWrapper<Books> booksQueryWrapper = new QueryWrapper<>();
            if (title != null && !title.trim().isEmpty()) {
                booksQueryWrapper.like("title", title);
            }
            if (author != null && !author.trim().isEmpty()) {
                booksQueryWrapper.like("author", author);
            }
            List<Books> books = bookDao.selectList(booksQueryWrapper);
            if (books != null && !books.isEmpty()) {
                return new ResponseBean<>(books, 200, "根据标题和作者查询图书成功");
            } else {
                return new ResponseBean<>(books, 201, "未找到匹配的图书");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "根据标题和作者查询图书失败：" + e.getMessage());
        }
    }
}
