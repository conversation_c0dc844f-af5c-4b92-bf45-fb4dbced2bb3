package com.laery0.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.laery0.dao.BookDao;
import com.laery0.dao.BookDetailsDao;
import com.laery0.dao.BookReviewsDao;
import com.laery0.po.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class BookServiceImpl implements BookService {
    @Autowired
    private BookDao bookDao;
    @Autowired
    private BookReviewsDao bookReviewsDao;
    @Autowired
    private BookDetailsDao bookDetailsDao;

    @Override
    public List<Books> selectAllBook() {
        return bookDao.selectList(null);
    }

    @Override
    public List<BooksAndDetails> selectBookAndDetails() {
        return bookDao.selectBookAndDetails();
    }

    @Override
    public List<BooksAndReviews> selectBookAndReviewsByBookId(Integer bookId) {
        return bookDao.selectBookAndReviewsByBookId(bookId);
    }

    @Override
    public int addBook(Books books) {
        return bookDao.insert(books);
    }

    @Override
    public int updateBook(Books books) {
        return bookDao.updateBook(books);
    }

    @Transactional
    @Override
    public Integer deleteBookAndRDByBookId(Integer bookId) {
        QueryWrapper<BookReviews> bookReviewsQueryWrapper = new QueryWrapper<>();
        bookReviewsQueryWrapper.eq("book_id", bookId);
        Integer deletedReviews = bookReviewsDao.delete(bookReviewsQueryWrapper);
        QueryWrapper<BookDetails> bookDetailsQueryWrapper = new QueryWrapper<>();
        bookDetailsQueryWrapper.eq("book_id", bookId);
        Integer deletedDetails = bookDetailsDao.delete(bookDetailsQueryWrapper);
//        Integer x = 10 / 0;
        QueryWrapper<Books> booksQueryWrapper = new QueryWrapper<>();
        booksQueryWrapper.eq("book_id", bookId);
        Integer deletedBook = bookDao.delete(booksQueryWrapper);
        deletedReviews = deletedReviews == null ? 0 : deletedReviews;
        deletedDetails = deletedDetails == null ? 0 : deletedDetails;
        deletedBook = deletedBook == null ? 0 : deletedBook;
        return deletedBook + deletedReviews + deletedDetails;
    }

    @Override
    public List<Books> selectBookByTitle(String title) {
        QueryWrapper<Books> booksQueryWrapper = new QueryWrapper<>();
        booksQueryWrapper.likeLeft("title", title);
        return bookDao.selectList(booksQueryWrapper);
    }

    @Override
    public List<Books> selectBookByTitleAndAuthor(String title, String author) {
        QueryWrapper<Books> booksQueryWrapper = new QueryWrapper<>();
        booksQueryWrapper.like("title", title).like("author", author);
        return bookDao.selectList(booksQueryWrapper);
    }
}
