package com.laery0.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laery0.dao.BookDao;
import com.laery0.dao.BookDetailDao;
import com.laery0.dao.BookReviewsDao;
import com.laery0.po.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

// 图书业务服务实现类
@Service
public class BookServiceImpl implements BookService {

    @Autowired
    private BookDao bookDao;

    @Autowired
    private BookReviewsDao bookReviewsDao;

    @Autowired
    private BookDetailDao bookDetailDao;

    @Override
    public ResponseBean<List<Book>> getAllBooks() {
        try {
            List<Book> books = bookDao.selectList(null);
            if (books != null && !books.isEmpty()) {
                return new ResponseBean<>(books, 200, "查询所有图书成功");
            } else {
                return new ResponseBean<>(books, 201, "暂无图书数据");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "查询所有图书失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<List<BookAndDetail>> getBooksWithDetails() {
        try {
            List<BookAndDetail> booksAndDetails = bookDao.findBooksWithDetails();
            if (booksAndDetails != null && !booksAndDetails.isEmpty()) {
                return new ResponseBean<>(booksAndDetails, 200, "查询图书详情成功");
            } else {
                return new ResponseBean<>(booksAndDetails, 201, "暂无图书详情数据");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "查询图书详情失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<Integer> createBook(Book book) {
        try {
            if (book == null) {
                return new ResponseBean<>(400, "图书信息不能为空");
            }
            if (book.getTitle() == null || book.getTitle().trim().isEmpty()) {
                return new ResponseBean<>(400, "图书标题不能为空");
            }
            if (book.getAuthor() == null || book.getAuthor().trim().isEmpty()) {
                return new ResponseBean<>(400, "图书作者不能为空");
            }
            int result = bookDao.insert(book);
            if (result > 0) {
                return new ResponseBean<>(result, 200, "添加图书成功");
            } else {
                return new ResponseBean<>(result, 300, "添加图书失败");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "添加图书失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<Integer> updateBook(Book book) {
        try {
            if (book == null) {
                return new ResponseBean<>(400, "图书信息不能为空");
            }
            if (book.getId() == null || book.getId() <= 0) {
                return new ResponseBean<>(400, "图书ID不能为空或小于等于0");
            }
            int result = bookDao.updateById(book);
            if (result > 0) {
                return new ResponseBean<>(result, 200, "更新图书成功");
            } else {
                return new ResponseBean<>(result, 300, "更新图书失败，可能图书不存在");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "更新图书失败：" + e.getMessage());
        }
    }

    @Transactional
    @Override
    public ResponseBean<Integer> deleteBookWithRelatedData(Integer bookId) {
        try {
            if (bookId == null || bookId <= 0) {
                return new ResponseBean<>(400, "图书ID不能为空或小于等于0");
            }
            Integer deletedReviews = bookReviewsDao.deleteById(bookId);
            Integer deletedDetails = bookDetailDao.delete(new QueryWrapper<BookDetail>().eq("book_id", bookId));
            // int x = 1 / 0;
            Integer deletedBook = bookDao.deleteById(bookId);
            deletedReviews = deletedReviews == null ? 0 : deletedReviews;
            deletedDetails = deletedDetails == null ? 0 : deletedDetails;
            deletedBook = deletedBook == null ? 0 : deletedBook;
            Integer totalDeleted = deletedBook + deletedReviews + deletedDetails;
            if (totalDeleted > 0) {
                return new ResponseBean<>(totalDeleted, 200, "删除图书及相关数据成功");
            } else {
                return new ResponseBean<>(totalDeleted, 300, "删除失败，可能图书不存在");
            }
        } catch (Exception e) {
            return new ResponseBean<>(500, "删除图书失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBean<List<Book>> getBooksPage(Double price, String author, Long currentPage, Long rows) {
        IPage<Book> booksIPage = new Page<>(currentPage, rows);
        QueryWrapper<Book> booksQueryWrapper = new QueryWrapper<>();
        booksQueryWrapper.eq("price", price);
        booksQueryWrapper.eq("author", author);
        IPage<Book> selectPage = bookDao.selectPage(booksIPage, booksQueryWrapper);
        List<Book> bookList = selectPage.getRecords();
        if(selectPage.getRecords() != null && !bookList.isEmpty()) {
            return new ResponseBean<>(bookList, 200, "查询图书成功");
        } else {
            return new ResponseBean<>(bookList, 201, "暂无图书数据");
        }
    }
}
