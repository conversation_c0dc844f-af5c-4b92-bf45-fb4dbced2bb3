package com.laery0.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.laery0.dao.AdminDao;
import com.laery0.po.Admin;
import com.laery0.po.ResponseBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 管理员业务服务实现类
 * <p>
 * 实现管理员相关的业务逻辑，主要包括管理员身份验证功能。
 * 该类负责处理管理员登录的具体业务逻辑，包括用户名验证、密码验证等。
 * 为了安全考虑，登录成功后会清空返回对象中的密码字段。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public class AdminServiceImpl implements AdminService {

    /**
     * 管理员数据访问对象
     */
    @Autowired
    private AdminDao adminDao;

    /**
     * 管理员登录验证实现
     * <p>
     * 实现管理员登录的具体业务逻辑：
     * 1. 根据用户名查询管理员信息
     * 2. 验证用户名是否存在
     * 3. 验证密码是否正确
     * 4. 登录成功后清空密码字段
     * </p>
     *
     * @param admin 包含用户名和密码的管理员对象
     * @return ResponseBean&lt;Admin&gt; 登录结果响应对象
     */
    @Override
    public ResponseBean<Admin> login(Admin admin) {
        // 构建查询条件，根据用户名查询管理员
        QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", admin.getUsername());
        Admin loginAdmin = adminDao.selectOne(queryWrapper);

        ResponseBean<Admin> adminResponseBean;

        // 验证用户名是否存在
        if (loginAdmin == null) {
            adminResponseBean = new ResponseBean<>(101, "用户名错误");
        } else {
            // 验证密码是否正确
            if (!loginAdmin.getPassword().equals(admin.getPassword())) {
                adminResponseBean = new ResponseBean<>(102, "密码错误");
            } else {
                // 登录成功，清空密码字段确保安全
                loginAdmin.setPassword(null);
                adminResponseBean = new ResponseBean<>(loginAdmin, 100, "登录成功");
            }
        }
        return adminResponseBean;
    }
}
