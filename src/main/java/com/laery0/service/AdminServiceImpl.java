package com.laery0.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.laery0.dao.AdminDao;
import com.laery0.po.Admin;
import com.laery0.po.ResponseBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminServiceImpl implements AdminService {
    @Autowired
    private AdminDao adminDao;

    @Override
    public ResponseBean<Admin> login(Admin admin) {
        QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", admin.getUsername());
        Admin loginAdmin = adminDao.selectOne(queryWrapper);
        ResponseBean<Admin> adminResponseBean = null;
        if (loginAdmin == null) {
            adminResponseBean = new ResponseBean<Admin>(101, "用户名错误");
        } else {
            if (!loginAdmin.getPassword().equals(admin.getPassword())) {
                adminResponseBean = new ResponseBean<Admin>(102, "密码错误");
            } else {
                loginAdmin.setPassword(null);
                adminResponseBean = new ResponseBean<Admin>(loginAdmin, 100, "登录成功");
            }
        }
        return adminResponseBean;
    }
}
