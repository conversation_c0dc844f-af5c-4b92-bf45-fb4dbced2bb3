package com.laery0.util;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component
public class DateConvert implements Converter<String, Date> {

    @Override
    public Date convert(String source) {
        if (source == null || source.trim().isEmpty()) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(source);
        } catch (ParseException e) {
            // 可以抛出运行时异常，也可以返回null或记录日志
            throw new IllegalArgumentException("日期格式错误，正确格式: yyyy-MM-dd，输入值: " + source, e);
        }
    }
}
