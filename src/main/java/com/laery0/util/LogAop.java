package com.laery0.util;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

@Component
@Aspect
@EnableAspectJAutoProxy
public class LogAop {
    private final static Logger logger = LoggerFactory.getLogger(LogAop.class);

    @Pointcut("execution(* com.laery0.controller.*.*(..))")
    private void cutPoint() {
    }

    @AfterThrowing(value = "cutPoint()", throwing = "exception")
    public void dealException(JoinPoint joinPoint, Exception exception) {
        logger.debug("异常信息:" + exception.getMessage());
    }
}
