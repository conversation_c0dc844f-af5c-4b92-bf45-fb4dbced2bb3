package com.laery0.controller;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;
import com.laery0.po.ResponseBean;
import com.laery0.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class BookController {
    @Autowired
    private BookService bookService;

    @RequestMapping("/selectAllBook")
    public ResponseBean<List<Books>> selectAllBook() {
        return bookService.selectAllBook();
    }

    @RequestMapping("/selectBookAndDetails")
    public ResponseBean<List<BooksAndDetails>> selectBookAndDetails() {
        return bookService.selectBookAndDetails();
    }

    @RequestMapping("/selectBookAndReviewsByBookId")
    public ResponseBean<List<BooksAndReviews>> selectBookAndReviewsByBookId(Integer bookId) {
        return bookService.selectBookAndReviewsByBookId(bookId);
    }

    @PostMapping("/addBook")
    public ResponseBean<Integer> addBook(@RequestBody Books books) {
        return bookService.addBook(books);
    }

    @RequestMapping("/updateBook")
    public ResponseBean<Integer> updateBook(@RequestBody Books books) {
        return bookService.updateBook(books);
    }

    @RequestMapping("/deleteBookAndRDByBookId")
    public ResponseBean<Integer> deleteBookAndRDByBookId(Integer bookId) {
        return bookService.deleteBookAndRDByBookId(bookId);
    }

    @RequestMapping("/selectBookByTitle")
    public ResponseBean<List<Books>> selectBookByTitle(String title) {
        return bookService.selectBookByTitle(title);
    }

    @RequestMapping("/selectBookByTitleAndAuthor")
    public ResponseBean<List<Books>> selectBookByTitleAndAuthor(String title, String author) {
        return bookService.selectBookByTitleAndAuthor(title, author);
    }
}
