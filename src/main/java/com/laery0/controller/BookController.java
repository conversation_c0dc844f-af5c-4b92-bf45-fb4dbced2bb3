package com.laery0.controller;

import com.laery0.po.Book;
import com.laery0.po.BookAndDetail;
import com.laery0.po.ResponseBean;
import com.laery0.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

// 图书管理控制器
@CrossOrigin("*")
@RestController
@RequestMapping("/api/books")
public class BookController {

    @Autowired
    private BookService bookService;

    // 获取所有图书
    @GetMapping
    public ResponseBean<List<Book>> getAllBooks() {
        return bookService.getAllBooks();
    }

    // 获取图书详情
    @GetMapping("/details")
    public ResponseBean<List<BookAndDetail>> getBooksWithDetails() {
        return bookService.getBooksWithDetails();
    }

    // 创建图书
    @PostMapping
    public ResponseBean<Integer> createBook(@RequestBody Book book) {
        return bookService.createBook(book);
    }

    // 更新图书
    @PutMapping("/{id}")
    public ResponseBean<Integer> updateBook(@PathVariable("id") Integer id, @RequestBody Book book) {
        book.setId(id);
        return bookService.updateBook(book);
    }

    // 删除图书及相关数据
    @DeleteMapping("/{id}")
    public ResponseBean<Integer> deleteBook(@PathVariable("id") Integer bookId) {
        return bookService.deleteBookWithRelatedData(bookId);
    }

    // 按图书价格和作者名分页查询图书
    @GetMapping("/page/{price}/{author}/{currentPage}/{rows}")
    public ResponseBean<List<Book>> getBooksPage(@PathVariable("price") Double price, @PathVariable("author") String author, @PathVariable("currentPage") Long currentPage, @PathVariable("rows") Long rows) {
        return bookService.getBooksPage(price, author, currentPage, rows);
    }
}
