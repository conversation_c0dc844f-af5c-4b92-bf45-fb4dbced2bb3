package com.laery0.controller;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;
import com.laery0.po.ResponseBean;
import com.laery0.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/books")
public class BookController {
    @Autowired
    private BookService bookService;

    // GET /api/books - 获取所有图书
    @GetMapping
    public ResponseBean<List<Books>> getAllBooks() {
        return bookService.getAllBooks();
    }

    // GET /api/books/details - 获取所有图书详情
    @GetMapping("/details")
    public ResponseBean<List<BooksAndDetails>> getBooksWithDetails() {
        return bookService.getBooksWithDetails();
    }

    // GET /api/books/{id}/reviews - 根据图书ID获取图书评论
    @GetMapping("/{id}/reviews")
    public ResponseBean<List<BooksAndReviews>> getBookReviewsById(@PathVariable("id") Integer bookId) {
        return bookService.getBookReviewsById(bookId);
    }

    // POST /api/books - 创建新图书
    @PostMapping
    public ResponseBean<Integer> createBook(@RequestBody Books books) {
        return bookService.createBook(books);
    }

    // PUT /api/books/{id} - 更新图书信息
    @PutMapping("/{id}")
    public ResponseBean<Integer> updateBook(@PathVariable("id") Integer id, @RequestBody Books books) {
        books.setId(id); // 确保ID一致
        return bookService.updateBook(books);
    }

    // DELETE /api/books/{id} - 删除图书及相关数据
    @DeleteMapping("/{id}")
    public ResponseBean<Integer> deleteBook(@PathVariable("id") Integer bookId) {
        return bookService.deleteBookWithRelatedData(bookId);
    }

    // GET /api/books/search/title - 根据标题搜索图书
    @GetMapping("/search/title")
    public ResponseBean<List<Books>> searchBooksByTitle(@RequestParam("title") String title) {
        return bookService.findBooksByTitle(title);
    }

    // GET /api/books/search - 根据标题和作者搜索图书
    @GetMapping("/search")
    public ResponseBean<List<Books>> searchBooks(@RequestParam(value = "title", required = false) String title,
                                                 @RequestParam(value = "author", required = false) String author) {
        return bookService.findBooksByTitleAndAuthor(title, author);
    }
}
