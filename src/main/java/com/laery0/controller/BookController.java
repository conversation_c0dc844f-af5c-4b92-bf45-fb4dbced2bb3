package com.laery0.controller;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;
import com.laery0.po.ResponseBean;
import com.laery0.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 图书管理控制器
 * <p>
 * 提供图书相关的RESTful API接口，包括图书的增删改查、搜索等功能。
 * 所有接口都返回统一的ResponseBean格式，便于前端处理。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/books")
public class BookController {

    /**
     * 图书业务服务层接口
     */
    @Autowired
    private BookService bookService;

    /**
     * 获取所有图书信息
     * <p>
     * 查询数据库中的所有图书记录，返回图书基本信息列表。
     * </p>
     *
     * @return ResponseBean&lt;List&lt;Books&gt;&gt; 包含图书列表的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=图书列表, message="查询所有图书成功"</li>
     *         <li>无数据时：status=201, data=空列表, message="暂无图书数据"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote GET /api/books
     * @since 1.0
     */
    @GetMapping
    public ResponseBean<List<Books>> getAllBooks() {
        return bookService.getAllBooks();
    }

    /**
     * 获取所有图书详情信息
     * <p>
     * 查询图书及其详细信息（包含图书描述、出版日期等），
     * 通过关联查询books表和book_details表获取完整信息。
     * </p>
     *
     * @return ResponseBean&lt;List&lt;BooksAndDetails&gt;&gt; 包含图书详情列表的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=图书详情列表, message="查询图书详情成功"</li>
     *         <li>无数据时：status=201, data=空列表, message="暂无图书详情数据"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote GET /api/books/details
     * @since 1.0
     */
    @GetMapping("/details")
    public ResponseBean<List<BooksAndDetails>> getBooksWithDetails() {
        return bookService.getBooksWithDetails();
    }

    /**
     * 根据图书ID获取图书评论信息
     * <p>
     * 通过图书ID查询该图书的所有评论信息，包括评分、评论者姓名、评论内容等。
     * </p>
     *
     * @param bookId 图书ID，必须为正整数
     * @return ResponseBean&lt;List&lt;BooksAndReviews&gt;&gt; 包含图书评论信息的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=图书评论列表, message="查询图书评论成功"</li>
     *         <li>无数据时：status=201, data=空列表, message="该图书暂无评论数据"</li>
     *         <li>参数错误时：status=400, data=null, message="图书ID不能为空或小于等于0"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote GET /api/books/{id}/reviews
     * @since 1.0
     */
    @GetMapping("/{id}/reviews")
    public ResponseBean<List<BooksAndReviews>> getBookReviewsById(@PathVariable("id") Integer bookId) {
        return bookService.getBookReviewsById(bookId);
    }

    /**
     * 创建新图书
     * <p>
     * 向系统中添加一本新图书，需要提供图书的基本信息。
     * 系统会自动验证必填字段（标题、作者）的有效性。
     * </p>
     *
     * @param books 图书信息对象，包含标题、作者、价格、库存等信息
     * @return ResponseBean&lt;Integer&gt; 包含操作结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=影响行数, message="添加图书成功"</li>
     *         <li>失败时：status=300, data=0, message="添加图书失败"</li>
     *         <li>参数错误时：status=400, data=null, message=具体错误信息</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote POST /api/books
     * @since 1.0
     */
    @PostMapping
    public ResponseBean<Integer> createBook(@RequestBody Books books) {
        return bookService.createBook(books);
    }

    /**
     * 更新图书信息
     * <p>
     * 根据图书ID更新图书的基本信息，支持部分字段更新。
     * 路径参数中的ID会自动设置到图书对象中，确保数据一致性。
     * </p>
     *
     * @param id    图书ID，必须为正整数
     * @param books 包含更新信息的图书对象
     * @return ResponseBean&lt;Integer&gt; 包含操作结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=影响行数, message="更新图书成功"</li>
     *         <li>失败时：status=300, data=0, message="更新图书失败，可能图书不存在"</li>
     *         <li>参数错误时：status=400, data=null, message=具体错误信息</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote PUT /api/books/{id}
     * @since 1.0
     */
    @PutMapping("/{id}")
    public ResponseBean<Integer> updateBook(@PathVariable("id") Integer id, @RequestBody Books books) {
        books.setId(id); // 确保ID一致
        return bookService.updateBook(books);
    }

    /**
     * 删除图书及相关数据
     * <p>
     * 根据图书ID删除图书记录，同时删除相关的详情信息和评论数据。
     * 该操作使用事务保证数据一致性，要么全部删除成功，要么全部回滚。
     * </p>
     *
     * @param bookId 图书ID，必须为正整数
     * @return ResponseBean&lt;Integer&gt; 包含操作结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=删除总行数, message="删除图书及相关数据成功"</li>
     *         <li>失败时：status=300, data=0, message="删除失败，可能图书不存在"</li>
     *         <li>参数错误时：status=400, data=null, message="图书ID不能为空或小于等于0"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote DELETE /api/books/{id}
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseBean<Integer> deleteBook(@PathVariable("id") Integer bookId) {
        return bookService.deleteBookWithRelatedData(bookId);
    }

    /**
     * 根据标题搜索图书
     * <p>
     * 使用模糊匹配的方式根据图书标题搜索图书信息。
     * 支持左模糊匹配，即搜索以指定标题结尾的图书。
     * </p>
     *
     * @param title 图书标题关键字，不能为空
     * @return ResponseBean&lt;List&lt;Books&gt;&gt; 包含搜索结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=匹配的图书列表, message="根据标题查询图书成功"</li>
     *         <li>无结果时：status=201, data=空列表, message="未找到匹配的图书"</li>
     *         <li>参数错误时：status=400, data=null, message="图书标题不能为空"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote GET /api/books/search/title?title=关键字
     * @since 1.0
     */
    @GetMapping("/search/title")
    public ResponseBean<List<Books>> searchBooksByTitle(@RequestParam("title") String title) {
        return bookService.findBooksByTitle(title);
    }

    /**
     * 根据标题和作者搜索图书
     * <p>
     * 支持同时根据图书标题和作者进行模糊搜索，两个参数都是可选的，
     * 但至少需要提供一个参数。支持单独按标题搜索、单独按作者搜索，
     * 或者同时按两个条件搜索。
     * </p>
     *
     * @param title  图书标题关键字，可选参数
     * @param author 作者姓名关键字，可选参数
     * @return ResponseBean&lt;List&lt;Books&gt;&gt; 包含搜索结果的响应对象
     *         <ul>
     *         <li>成功时：status=200, data=匹配的图书列表, message="根据标题和作者查询图书成功"</li>
     *         <li>无结果时：status=201, data=空列表, message="未找到匹配的图书"</li>
     *         <li>参数错误时：status=400, data=null, message="图书标题和作者不能同时为空"</li>
     *         <li>异常时：status=500, data=null, message=错误信息</li>
     *         </ul>
     * @apiNote GET /api/books/search?title=标题关键字&author=作者关键字
     * @since 1.0
     */
    @GetMapping("/search")
    public ResponseBean<List<Books>> searchBooks(@RequestParam(value = "title", required = false) String title,
                                                 @RequestParam(value = "author", required = false) String author) {
        return bookService.findBooksByTitleAndAuthor(title, author);
    }
}
