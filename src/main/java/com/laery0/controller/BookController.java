package com.laery0.controller;

import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;
import com.laery0.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class BookController {
    @Autowired
    private BookService bookService;

    @RequestMapping("/selectAllBook")
    public List<Books> selectAllBook() {
        return bookService.selectAllBook();
    }

    @RequestMapping("/selectBookAndDetails")
    public List<BooksAndDetails> selectBookAndDetails() {
        return bookService.selectBookAndDetails();
    }

    @RequestMapping("/selectBookAndReviewsByBookId")
    public List<BooksAndReviews> selectBookAndReviewsByBookId(Integer bookId) {
        return bookService.selectBookAndReviewsByBookId(bookId);
    }

    @PostMapping("/addBook")
    public int addBook(@RequestBody Books books) {
        return bookService.addBook(books);
    }

    @RequestMapping("/updateBook")
    public int updateBook(@RequestBody Books books) {
        return bookService.updateBook(books);
    }

    @RequestMapping("/deleteBookAndRDByBookId")
    public int deleteBookAndRDByBookId(Integer bookId) {
        return bookService.deleteBookAndRDByBookId(bookId);
    }

    @RequestMapping("/selectBookByTitle")
    public List<Books> selectBookByTitle(String title) {
        return bookService.selectBookByTitle(title);
    }

    @RequestMapping("/selectBookByTitleAndAuthor")
    public List<Books> selectBookByTitleAndAuthor(String title, String author) {
        return bookService.selectBookByTitleAndAuthor(title, author);
    }
}
