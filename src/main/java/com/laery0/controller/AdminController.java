package com.laery0.controller;

import com.laery0.po.Admin;
import com.laery0.po.ResponseBean;
import com.laery0.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

// 管理员控制器
@CrossOrigin("*")
@RestController
public class AdminController {

    @Autowired
    private AdminService adminService;

    // 管理员登录
    @PostMapping("/login")
    public ResponseBean<Admin> login(@RequestBody Admin admin) {
        return adminService.login(admin);
    }
}
