package com.laery0.controller;

import com.laery0.po.Admin;
import com.laery0.po.ResponseBean;
import com.laery0.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理员控制器
 * <p>
 * 提供管理员相关的API接口，主要包括管理员登录功能。
 * 负责处理管理员身份验证和权限管理相关的HTTP请求。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
public class AdminController {

    /**
     * 管理员业务服务层接口
     */
    @Autowired
    private AdminService adminService;

    /**
     * 管理员登录
     * <p>
     * 验证管理员的用户名和密码，成功登录后返回管理员信息（密码已清空）。
     * 登录过程包括用户名存在性验证和密码正确性验证。
     * </p>
     *
     * @param admin 包含用户名和密码的管理员对象
     * @return ResponseBean&lt;Admin&gt; 包含登录结果的响应对象
     *         <ul>
     *         <li>登录成功：status=100, data=管理员信息（密码已清空）, message="登录成功"</li>
     *         <li>用户名错误：status=101, data=null, message="用户名错误"</li>
     *         <li>密码错误：status=102, data=null, message="密码错误"</li>
     *         </ul>
     * @apiNote POST /login
     * @since 1.0
     */
    @PostMapping("/login")
    public ResponseBean<Admin> login(@RequestBody Admin admin) {
        return adminService.login(admin);
    }
}
