package com.laery0.controller;

import com.laery0.po.Admin;
import com.laery0.po.ResponseBean;
import com.laery0.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AdminController {
    @Autowired
    private AdminService adminService;

    @PostMapping("/login")
    public ResponseBean<Admin> login(@RequestBody Admin admin) {
        return adminService.login(admin);
    }
}
