package com.laery0.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laery0.po.Admin;

/**
 * 管理员数据访问接口
 * <p>
 * 继承MyBatis-Plus的BaseMapper，提供管理员实体的基础CRUD操作。
 * 包括插入、删除、更新、查询等标准数据库操作方法。
 * 目前使用MyBatis-Plus提供的默认方法即可满足业务需求。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AdminDao extends BaseMapper<Admin> {
    // 目前使用MyBatis-Plus提供的基础方法即可满足需求
    // 如需要自定义查询方法，可在此处添加方法声明
}
