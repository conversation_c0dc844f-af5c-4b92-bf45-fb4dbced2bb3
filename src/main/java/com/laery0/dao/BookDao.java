package com.laery0.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;

import java.util.List;

/**
 * 图书数据访问接口
 * <p>
 * 继承MyBatis-Plus的BaseMapper，提供基础的CRUD操作。
 * 同时定义了图书相关的自定义查询方法，包括关联查询和复杂查询。
 * 该接口通过MyBatis的XML映射文件实现具体的SQL逻辑。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface BookDao extends BaseMapper<Books> {

    /**
     * 查询所有图书及其详情信息
     * <p>
     * 通过关联查询books表和book_details表，获取图书的完整信息。
     * 返回的结果包含图书基本信息和详细描述、出版日期等扩展信息。
     * </p>
     *
     * @return List&lt;BooksAndDetails&gt; 图书详情列表，如果没有数据则返回空列表
     * @see BooksAndDetails
     * @since 1.0
     */
    List<BooksAndDetails> findBooksWithDetails();

    /**
     * 根据图书ID查询图书及其评论信息
     * <p>
     * 通过关联查询books表和book_reviews表，获取指定图书的评论信息。
     * 返回的结果包含图书基本信息和该图书的所有评论列表。
     * </p>
     *
     * @param bookId 图书ID，不能为null
     * @return List&lt;BooksAndReviews&gt; 图书评论列表，如果没有评论则返回空列表
     * @see BooksAndReviews
     * @since 1.0
     */
    List<BooksAndReviews> findBookWithReviewsById(Integer bookId);

    /**
     * 选择性更新图书信息
     * <p>
     * 根据图书ID更新图书信息，只更新非空字段，空字段保持原值不变。
     * 这是一个自定义的更新方法，提供比MyBatis-Plus默认updateById更灵活的更新逻辑。
     * </p>
     *
     * @param books 包含更新信息的图书对象，ID字段必须有效
     * @return int 影响的行数，通常为1表示更新成功，0表示没有找到对应记录
     * @since 1.0
     */
    int updateBookSelective(Books books);
}
