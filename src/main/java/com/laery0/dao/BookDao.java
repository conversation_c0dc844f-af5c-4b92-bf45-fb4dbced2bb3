package com.laery0.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laery0.po.Books;
import com.laery0.po.BooksAndDetails;
import com.laery0.po.BooksAndReviews;

import java.util.List;

public interface BookDao extends BaseMapper<Books> {
    /**
     * 查询所有图书及其详情
     */
    List<BooksAndDetails> findBooksWithDetails();

    /**
     * 根据图书ID查询图书及其评论
     */
    List<BooksAndReviews> findBookWithReviewsById(Integer bookId);

    /**
     * 更新图书信息（自定义更新逻辑）
     * 注意：MyBatis-Plus已提供updateById方法，此方法用于特殊更新需求
     */
    int updateBookSelective(Books books);
}
