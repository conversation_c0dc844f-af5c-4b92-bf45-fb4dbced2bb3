package com.laery0.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laery0.po.BookDetails;

/**
 * 图书详情数据访问接口
 * <p>
 * 继承MyBatis-Plus的BaseMapper，提供图书详情实体的基础CRUD操作。
 * 图书详情表存储图书的扩展信息，如详细描述、出版日期等。
 * 与图书主表通过book_id字段建立关联关系。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface BookDetailsDao extends BaseMapper<BookDetails> {
    // 目前使用MyBatis-Plus提供的基础方法即可满足需求
    // 如需要自定义查询方法，可在此处添加方法声明
}
