package com.laery0;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Spring Boot 应用程序启动类
 * <p>
 * 图书管理系统的主启动类，负责启动整个Spring Boot应用程序。
 * 该类配置了组件扫描和MyBatis Mapper扫描，是应用程序的入口点。
 * </p>
 *
 * <h3>主要功能：</h3>
 * <ul>
 * <li>启动Spring Boot应用程序</li>
 * <li>自动配置Spring容器</li>
 * <li>扫描并注册所有组件（Controller、Service、Repository等）</li>
 * <li>配置MyBatis Mapper接口扫描</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@SpringBootApplication
@MapperScan("com.laery0.dao") // 扫描Dao层接口，自动创建Mapper实现类
public class Springboot01Application {

    /**
     * 应用程序主入口方法
     * <p>
     * 启动Spring Boot应用程序，初始化Spring容器，
     * 加载配置文件，启动内嵌的Web服务器。
     * </p>
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(Springboot01Application.class, args);
    }
}
