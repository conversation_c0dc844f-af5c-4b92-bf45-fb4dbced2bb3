<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.laery0.dao.BookDao">
    <resultMap id="findBooksWithDetailsMap" type="com.laery0.po.BooksAndDetails">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <association property="bookDetails" javaType="com.laery0.po.BookDetails">
            <result property="description" column="description"/>
            <result property="publish_Date" column="publish_Date"/>
        </association>
    </resultMap>

    <select id="findBooksWithDetails" resultMap="findBooksWithDetailsMap">
        select * from books join book_details on books.id = book_details.book_id;
    </select>
    
    <resultMap id="findBookWithReviewsByIdMap" type="com.laery0.po.BooksAndReviews">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <collection property="bookReviewsList" ofType="com.laery0.po.BookReviews">
            <result property="rating" column="rating"/>
            <result property="reviewer_Name" column="reviewer_Name"/>
            <result property="review_Text" column="review_Text"/>
        </collection>
    </resultMap>

    <select id="findBookWithReviewsById" resultMap="findBookWithReviewsByIdMap" parameterType="int">
        select * from books join book_reviews on books.id = book_reviews.book_id where book_id = #{bookId};
    </select>
    
    <update id="updateBookSelective" parameterType="com.laery0.po.Books">
        update books
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="author != null and author != ''">
                author = #{author},
            </if>
            <if test="price != null and price >= 0">
                price = #{price},
            </if>
            <if test="stock != null and stock >= 0">
                stock = #{stock},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>