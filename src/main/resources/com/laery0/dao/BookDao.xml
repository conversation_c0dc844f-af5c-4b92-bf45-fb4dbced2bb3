<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laery0.dao.BookDao">

    <!-- 查询图书及其详细 -->
    <resultMap id="findBookWithDetailMap" type="com.laery0.po.BookAndDetail">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <association property="bookDetail" javaType="com.laery0.po.BookDetail" select="com.laery0.dao.BookDetailDao.getBookAndDetailById" column="id">
            <result property="description" column="description"/>
            <result property="publishDate" column="publishDate"/>
        </association>
    </resultMap>

    <select id="findBookWithDetail" resultMap="findBookWithDetailMap">
        SELECT b.*, bd.description, bd.publish_date
        FROM books b
                 JOIN book_details bd ON b.id = bd.book_id
    </select>

    <resultMap id="findBookWithReviewsByIdMap" type="com.laery0.po.BookAndReview">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <collection property="bookReviewList" ofType="com.laery0.po.BookReview">
            <result property="rating" column="rating"/>
            <result property="reviewerName" column="reviewerName"/>
            <result property="reviewText" column="reviewText"/>
        </collection>
    </resultMap>
</mapper>