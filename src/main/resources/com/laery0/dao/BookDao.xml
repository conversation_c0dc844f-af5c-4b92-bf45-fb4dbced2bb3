<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
    图书数据访问层 MyBatis 映射文件

    该文件定义了BookDao接口中自定义方法的SQL实现，
    包括复杂的关联查询和自定义更新操作。

    作者: laery0
    版本: 1.0
    创建时间: 2024-01-01
-->
<mapper namespace="com.laery0.dao.BookDao">

    <!--
        图书详情关联查询结果映射
        用于映射books表和book_details表的关联查询结果
    -->
    <resultMap id="findBooksWithDetailsMap" type="com.laery0.po.BooksAndDetails">
        <!-- 图书基本信息映射 -->
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <!-- 图书详情信息关联映射 -->
        <association property="bookDetails" javaType="com.laery0.po.BookDetails">
            <result property="description" column="description"/>
            <result property="publish_Date" column="publish_Date"/>
        </association>
    </resultMap>

    <!--
        查询所有图书及其详情信息
        通过内连接获取图书基本信息和详情信息
    -->
    <select id="findBooksWithDetails" resultMap="findBooksWithDetailsMap">
        SELECT b.*, bd.description, bd.publish_Date
        FROM books b
        INNER JOIN book_details bd ON b.id = bd.book_id
    </select>

    <!--
        图书评论关联查询结果映射
        用于映射books表和book_reviews表的关联查询结果
    -->
    <resultMap id="findBookWithReviewsByIdMap" type="com.laery0.po.BooksAndReviews">
        <!-- 图书基本信息映射 -->
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <!-- 图书评论列表集合映射 -->
        <collection property="bookReviewsList" ofType="com.laery0.po.BookReviews">
            <result property="rating" column="rating"/>
            <result property="reviewer_Name" column="reviewer_Name"/>
            <result property="review_Text" column="review_Text"/>
        </collection>
    </resultMap>

    <!--
        根据图书ID查询图书及其评论信息
        通过内连接获取指定图书的所有评论
    -->
    <select id="findBookWithReviewsById" resultMap="findBookWithReviewsByIdMap" parameterType="int">
        SELECT b.*, br.rating, br.reviewer_Name, br.review_Text
        FROM books b
        INNER JOIN book_reviews br ON b.id = br.book_id
        WHERE b.id = #{bookId}
    </select>

    <!--
        选择性更新图书信息
        只更新非空字段，空字段保持原值不变
        使用动态SQL实现灵活的更新操作
    -->
    <update id="updateBookSelective" parameterType="com.laery0.po.Books">
        UPDATE books
        <set>
            <!-- 标题不为空且不为空字符串时更新 -->
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <!-- 作者不为空且不为空字符串时更新 -->
            <if test="author != null and author != ''">
                author = #{author},
            </if>
            <!-- 价格不为空且大于等于0时更新 -->
            <if test="price != null and price >= 0">
                price = #{price},
            </if>
            <!-- 库存不为空且大于等于0时更新 -->
            <if test="stock != null and stock >= 0">
                stock = #{stock},
            </if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>