# Tomcat端口
server.port=8080
# 访问路径
server.servlet.context-path=/booksystem
# 日志
logging.level.root=debug
logging.level.org.springframework=debug
logging.level.com.baomidou.mybatisplus=debug
logging.level.org.mybatis=debug
logging.level.org.apache.ibatis=debug
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
# 数据库连接
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=****************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
# Mybatis-Plus的xml位置
mybatis-plus.mapper-locations=classpath:com/laery0/dao/*.xml
# Mybatis-Plus实体类位置
mybatis-plus.type-aliases-package=com.laery0.po

