# ========================================
# 图书管理系统 - Spring Boot 配置文件
# ========================================
#
# 该配置文件包含了图书管理系统的所有核心配置项，
# 包括服务器配置、数据库连接、日志配置、MyBatis-Plus配置等。
#
# 作者: laery0
# 版本: 1.0
# 创建时间: 2024-01-01
# ========================================

# ========================================
# 服务器配置
# ========================================
# Tomcat服务器端口号，默认8080
server.port=8080
# 应用程序上下文路径，所有API请求都需要加上此前缀
server.servlet.context-path=/booksystem

# ========================================
# 日志配置
# ========================================
# 根日志级别设置为DEBUG，便于开发调试
logging.level.root=debug
# Spring框架日志级别
logging.level.org.springframework=debug
# MyBatis-Plus框架日志级别
logging.level.com.baomidou.mybatisplus=debug
# MyBatis框架日志级别
logging.level.org.mybatis=debug
# Apache iBatis日志级别
logging.level.org.apache.ibatis=debug
# MyBatis-Plus SQL日志输出实现，控制台输出SQL语句
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# ========================================
# 数据库连接配置
# ========================================
# MySQL数据库驱动类
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# 数据库连接URL，连接到本地MySQL的homework数据库
# 参数说明：
# - useUnicode=true: 使用Unicode字符集
# - characterEncoding=utf8: 字符编码设置为UTF-8
# - useSSL=false: 禁用SSL连接（开发环境）
# - serverTimezone=UTC: 设置服务器时区为UTC
# - allowPublicKeyRetrieval=true: 允许客户端从服务器获取公钥
spring.datasource.url=****************************************************************************************************************************************
# 数据库用户名
spring.datasource.username=root
# 数据库密码
spring.datasource.password=root

# ========================================
# MyBatis-Plus 配置
# ========================================
# MyBatis XML映射文件位置，扫描dao包下的所有XML文件
mybatis-plus.mapper-locations=classpath:com/laery0/dao/*.xml
# 实体类包路径，用于类型别名设置
mybatis-plus.type-aliases-package=com.laery0.po
# 命名转换规则
mybatis-plus.configuration.map-underscore-to-camel-case=true

