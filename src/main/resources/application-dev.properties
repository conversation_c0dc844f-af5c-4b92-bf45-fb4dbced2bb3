# Tomcat服务器端口号，默认8080
server.port=8888
# 应用程序上下文路径，所有API请求都需要加上此前缀
server.servlet.context-path=/
# 根日志级别设置为DEBUG，便于开发调试
logging.level.root=debug
# 整个项目的日志级别
logging.level.com.laery0=debug
# 颗粒度更细的Mybatis Plus日志
logging.level.com.laery0.dao=debug
# 颗粒度更新的spring日志
logging.level.org.springframework=info
# 让AOP日志处理类可以把日志写入日志文件，并在控制台输出
logging.level.com.laery0.util.LogAop=debug
# 把日志写入特定文件中(注意盘符后有两个斜线)
logging.file.name=D://000Files/Code/Log/log.txt
# MySQL数据库驱动类
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# 数据库连接URL，连接到本地MySQL的homework数据库
spring.datasource.url=****************************************************************************************************************************************
# 数据库用户名
spring.datasource.username=root
# 数据库密码
spring.datasource.password=root
# MyBatis XML映射文件位置，扫描dao包下的所有XML文件
mybatis-plus.mapper-locations=classpath:com/laery0/dao/*.xml
# 实体类包路径，用于类型别名设置
mybatis-plus.type-aliases-package=com.laery0.po
# 命名转换规则
mybatis-plus.configuration.map-underscore-to-camel-case=true

